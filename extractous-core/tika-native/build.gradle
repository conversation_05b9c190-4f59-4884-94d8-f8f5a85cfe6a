plugins {
    id 'java-library'
    id'org.graalvm.buildtools.native' version '0.10.3' // GraalVM plugin for Gradle
}


def tikaVersion = "2.9.2"
def numThreads = Runtime.getRuntime().availableProcessors()
def currentOs = org.gradle.internal.os.OperatingSystem.current()


def osName = ""
if (currentOs.isLinux()) {
    osName = "linux"
} else if (currentOs.isMacOsX()) {
    osName = "macos"
} else if (currentOs.isWindows()) {
    osName = "windows"
}

group = 'ai.yobix'
version = "tika-$tikaVersion-$osName"


repositories {
    mavenCentral()
}

dependencies {
    // Tika uses slf4j, just use a nop logger to ignore all logging
    implementation("org.slf4j:slf4j-nop:2.0.11")
    // Some dependencies use log4j such as poi, route log4j back to slf4j
    // Had to use 3.0.0-beta2 because it is solves some issues with log4j to make it graalvm native friendly
    implementation 'org.apache.logging.log4j:log4j-to-slf4j:3.0.0-beta2'

    implementation("org.apache.tika:tika-core:$tikaVersion")
    implementation "org.apache.tika:tika-parsers-standard:$tikaVersion" // Apache Tika parsers

    implementation("org.apache.tika:tika-parser-microsoft-module:$tikaVersion")
    implementation "org.apache.tika:tika-parser-pdf-module:$tikaVersion"
    implementation "org.apache.tika:tika-parser-html-module:$tikaVersion"
    implementation "org.apache.tika:tika-parser-apple-module:$tikaVersion"
    implementation "org.apache.tika:tika-parser-audiovideo-module:$tikaVersion"
    implementation "org.apache.tika:tika-parser-cad-module:$tikaVersion"
    implementation "org.apache.tika:tika-parser-crypto-module:$tikaVersion"
    implementation "org.apache.tika:tika-parser-font-module:$tikaVersion"
    implementation "org.apache.tika:tika-parser-html-commons:$tikaVersion"
    implementation "org.apache.tika:tika-parser-image-module:$tikaVersion"
    implementation "org.apache.tika:tika-parser-mail-module:$tikaVersion"
    implementation "org.apache.tika:tika-parser-miscoffice-module:$tikaVersion"
    implementation "org.apache.tika:tika-parser-news-module:$tikaVersion"
    implementation "org.apache.tika:tika-parser-ocr-module:$tikaVersion"
    implementation "org.apache.tika:tika-parser-pkg-module:$tikaVersion"
    implementation "org.apache.tika:tika-parser-text-module:$tikaVersion"
    implementation "org.apache.tika:tika-parser-xml-module:$tikaVersion"
    implementation "org.apache.tika:tika-parser-webarchive-module:$tikaVersion"
}

graalvmNative {
    // Make sure this is off to use the same GraalVM version thant the one used by gradle
    // Setting the version is left outside of this script in github workflow or developer build environment
    toolchainDetection = false

    // Set to false to disable to run the tests in native mode.
    testSupport = true

    binaries {
        main {
            imageName = 'libtika_native'

            // To build a shared lib, make sure to not set the mainClass
            sharedLibrary = true

            // The --no-fallback option to native-image causes the utility to fail if it can not create the image.
            fallback = false

            configurationFileDirectories.from(file("src/main/resources/META-INF/$group/$version"))

            buildArgs.addAll(
                    "-H:+AddAllCharsets", // Very important to get UTF8 working
                    "--enable-https", // Very important https working
                    "-O3",
                    "--parallelism=$numThreads",
                    "-march=compatibility" // VERY IMPORTANT to use compatibility flag. If not the libs will use the cpu arch of the build machine and will notwork on other CPUs if distributed
            )
            jvmArgs.add('-Djava.awt.headless=true')
            requiredVersion = '23' // The minimal GraalVM version, can be `MAJOR`, `MAJOR.MINOR` or `MAJOR.MINOR.PATCH`
        }
    }
}