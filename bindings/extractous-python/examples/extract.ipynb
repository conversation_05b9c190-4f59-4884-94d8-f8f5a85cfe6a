{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install extractous\n", "%pip install extractous"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Extracting a file to string"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "from extractous import Extractor\n", "\n", "extractor = Extractor()\n", "extractor.set_extract_string_max_length(1000)\n", "result = extractor.extract_file_to_string(\"README.md\")\n", "\n", "print(result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Extracting a file to a buffered stream"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from extractous import Extractor\n", "\n", "extractor = Extractor()\n", "reader = extractor.extract_file(\"tests/quarkus.pdf\")\n", "\n", "result = \"\"\n", "buffer = reader.read(4096)\n", "while len(buffer) > 0:\n", "    result += buffer.decode(\"utf-8\")\n", "    buffer = reader.read(4096)\n", "\n", "print(result)"]}], "metadata": {"kernelspec": {"display_name": "extractous-python", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}