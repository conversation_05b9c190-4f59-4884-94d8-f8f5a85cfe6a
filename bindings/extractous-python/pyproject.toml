[build-system]
requires = ["maturin>=1.0,<2.0"]
build-backend = "maturin"

[project]
name = "extractous"
version = '0.2.1'
classifiers = [
  "Programming Language :: Rust",
  "Programming Language :: Python :: Implementation :: CPython",
  "Programming Language :: Python :: Implementation :: PyPy",
]
description = "Extractous Python Binding"
license = { text = "Apache-2.0" }
readme = "README.md"
requires-python = ">=3.8,<3.14"

[project.optional-dependencies]
# To generate python docs using pdoc we need to run:
# maturin develop -E docs
# pdoc extractous -o docs --no-search --no-show-source --logo-link "https://yobix.ai"  --logo "https://avatars.githubusercontent.com/u/137815199?s=48&v=4"
docs = ["pdoc"]
# To run tests using pytest we need to run:
# pytest -s
test = ["pytest","scikit-learn"]

[project.urls]
Documentation = "https://extractous.yobix.ai/docs/python/index.html"
Homepage = "https://extractous.yobix.ai/"
Repository = "https://github.com/yobix-ai/extractous"

[tool.maturin]
# -------------- Cargo options --------------
# Build artifacts with the specified Cargo profile
profile = "release"
# "extension-module" tells pyo3 we want to build an extension module (skips linking against libpython.so)
features = ["pyo3/extension-module"]
# -------------- Cargo options --------------

strip=true

# It is a comman practice to keep the pyo3 module private and import it within a global python module
# extractous: is the pure python module that is exported
# _extractous: is the private pyo3 module, the name must match the pyo3 module function name
module-name = "extractous._extractous"

# The source folder where the python code is
# this folder should contain a directory named extractous matching our pure python exported modules
python-source = "python"

# Setting skip-auditwheel=true is very important to instruct maturin to not run its auditwheel flow
# maturin auditwheel flow changes any top level shared lib names which causes problems with our graalvm libs
# By skipping the wheel, we just get a plain _extracts_rs* lib, and we have to:
#   * bundle our graalvm libs using the below include [] directive
#   * change the RPATH of _extracts_rs* lib to be able to properly find the bundled graalvm libs
skip-auditwheel=true

# This tells cargo to set the RPATH for the private module built lib _extractous.abi3.so
# Set the RPATH to $ORIGIN because the graalvm libs will be bundled in the same dir as the _extractous.abi3.so
rustc-args = ["-C", "link-arg=-Wl,-rpath,$ORIGIN"]

# Maturin include command will start looking from the python/extractous folder
# so to include the graalvm libs the rust build script must copy them to python/extractous folder
include = [
  {path = "**/*.so", format = ["wheel"]},
  {path = "**/*.dylib", format = ["wheel"]},
  {path = "**/*.dll", format = ["wheel"]}
]
