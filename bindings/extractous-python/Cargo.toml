[package]
name = "extractous-python"
publish = false

authors = ["Yobix AI <<EMAIL>>"]
edition = "2021"
homepage = "https://extractous.yobix.ai/"
license = "Apache-2.0"
repository = "https://github.com/yobix-ai/extractous"
rust-version = "1.75"
version = "0.1.3"


[lib]
name = "extractous" # This has to match the folder name under python foler i.e. python/extractous/
crate-type = ["cdylib"]
doc = false

[dependencies]
# "abi3-py310" tells pyo3 (and maturin) to build using the stable ABI with minimum Python version 3.10
pyo3 = { version = "0.23.3", features = ["abi3", "abi3-py38"] }
extractous = { path = "../../extractous-core" }
